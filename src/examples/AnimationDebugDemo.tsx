import React, { useRef, useState } from "react";
import { useLoadingAnimation } from "../hooks/useLoadingAnimation";
import "../stories/Button/button.scss";

export const AnimationDebugDemo: React.FC = () => {
  return (
    <div style={{ padding: "20px", maxWidth: "800px" }}>
      <h2>Loading Animation Debug Demo</h2>
      <p>This demo helps identify and fix the animation issues you mentioned:</p>

      <div style={{ display: "grid", gap: "30px", marginTop: "20px" }}>
        <PrimaryButton />
        <SecondaryButton />
        <CustomStyledButton />
        <HighContrastButton />
      </div>

      <div style={{ marginTop: "40px", padding: "20px", backgroundColor: "#f5f5f5", borderRadius: "8px" }}>
        <h3>Issues Being Fixed:</h3>
        <ul>
          <li>
            <strong>No border visible during spin:</strong> Using setProperty with !important
          </li>
          <li>
            <strong>No color during spin:</strong> Using high contrast colors and forced styles
          </li>
          <li>
            <strong>Background not restored:</strong> Saving and restoring original className and inline styles
          </li>
        </ul>
      </div>
    </div>
  );
};

const PrimaryButton: React.FC = () => {
  const containerRef = useRef<HTMLElement>(null);
  const textRef = useRef<HTMLElement>(null);
  const iconRef = useRef<HTMLElement>(null);
  const [isLoading, setIsLoading] = useState(false);

  const { startLoading, goBack, cleanup } = useLoadingAnimation({
    borderColor: "#ffffff", // White border on blue background
    circleSize: 50,
    spinDuration: 1500,
  });

  React.useEffect(() => cleanup, [cleanup]);

  const handleClick = () => {
    if (!isLoading) {
      setIsLoading(true);
      startLoading({ containerRef, textRef, iconRef });
      setTimeout(() => {
        setIsLoading(false);
        goBack({ containerRef, textRef, iconRef });
      }, 4000);
    }
  };

  return (
    <div>
      <h4>Primary Button (Blue with White Border)</h4>
      <button
        ref={containerRef as React.RefObject<HTMLButtonElement>}
        onClick={handleClick}
        disabled={isLoading}
        className="kino-button kino-button--primary kino-button--medium"
        style={{ position: "relative" }}
      >
        <span ref={textRef as React.RefObject<HTMLSpanElement>}>Primary Action</span>
        <div
          ref={iconRef as React.RefObject<HTMLDivElement>}
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%) scale(0)",
            opacity: 0,
            color: "white",
            fontSize: "18px",
          }}
        >
          ⚡
        </div>
      </button>
    </div>
  );
};

const SecondaryButton: React.FC = () => {
  const containerRef = useRef<HTMLElement>(null);
  const textRef = useRef<HTMLElement>(null);
  const iconRef = useRef<HTMLElement>(null);
  const [isLoading, setIsLoading] = useState(false);

  const { startLoading, goBack, cleanup } = useLoadingAnimation({
    borderColor: "#555ab9", // Blue border on transparent background
    circleSize: 50,
    spinDuration: 1500,
  });

  React.useEffect(() => cleanup, [cleanup]);

  const handleClick = () => {
    if (!isLoading) {
      setIsLoading(true);
      startLoading({ containerRef, textRef, iconRef });
      setTimeout(() => {
        setIsLoading(false);
        goBack({ containerRef, textRef, iconRef });
      }, 4000);
    }
  };

  return (
    <div>
      <h4>Secondary Button (Transparent with Blue Border)</h4>
      <button
        ref={containerRef as React.RefObject<HTMLButtonElement>}
        onClick={handleClick}
        disabled={isLoading}
        className="kino-button kino-button--secondary kino-button--medium"
        style={{ position: "relative" }}
      >
        <span ref={textRef as React.RefObject<HTMLSpanElement>}>Secondary Action</span>
        <div
          ref={iconRef as React.RefObject<HTMLDivElement>}
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%) scale(0)",
            opacity: 0,
            color: "#555ab9",
            fontSize: "18px",
          }}
        >
          🔄
        </div>
      </button>
    </div>
  );
};

const CustomStyledButton: React.FC = () => {
  const containerRef = useRef<HTMLElement>(null);
  const textRef = useRef<HTMLElement>(null);
  const iconRef = useRef<HTMLElement>(null);
  const [isLoading, setIsLoading] = useState(false);

  const { startLoading, goBack, cleanup } = useLoadingAnimation({
    borderColor: "#ff4444", // Bright red border
    circleSize: 55,
    spinDuration: 1200,
    easing: "easeOutBack",
  });

  React.useEffect(() => cleanup, [cleanup]);

  const handleClick = () => {
    if (!isLoading) {
      setIsLoading(true);
      startLoading({ containerRef, textRef, iconRef });
      setTimeout(() => {
        setIsLoading(false);
        goBack({ containerRef, textRef, iconRef });
      }, 4000);
    }
  };

  return (
    <div>
      <h4>Custom Styled Button (Green with Red Border)</h4>
      <button
        ref={containerRef as React.RefObject<HTMLButtonElement>}
        onClick={handleClick}
        disabled={isLoading}
        style={{
          padding: "12px 24px",
          borderRadius: "8px",
          border: "2px solid #28a745",
          backgroundColor: "#28a745",
          color: "white",
          cursor: isLoading ? "not-allowed" : "pointer",
          position: "relative",
          minWidth: "150px",
          fontSize: "14px",
          fontWeight: "bold",
        }}
      >
        <span ref={textRef as React.RefObject<HTMLSpanElement>}>Custom Button</span>
        <div
          ref={iconRef as React.RefObject<HTMLDivElement>}
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%) scale(0)",
            opacity: 0,
            color: "white",
            fontSize: "18px",
          }}
        >
          ✓
        </div>
      </button>
    </div>
  );
};

const HighContrastButton: React.FC = () => {
  const containerRef = useRef<HTMLElement>(null);
  const textRef = useRef<HTMLElement>(null);
  const iconRef = useRef<HTMLElement>(null);
  const [isLoading, setIsLoading] = useState(false);

  const { startLoading, goBack, cleanup } = useLoadingAnimation({
    borderColor: "#ffff00", // Bright yellow border
    circleSize: 60,
    spinDuration: 2000,
    shapeDuration: 800,
  });

  React.useEffect(() => cleanup, [cleanup]);

  const handleClick = () => {
    if (!isLoading) {
      setIsLoading(true);
      startLoading({ containerRef, textRef, iconRef });
      setTimeout(() => {
        setIsLoading(false);
        goBack({ containerRef, textRef, iconRef });
      }, 6000);
    }
  };

  return (
    <div>
      <h4>High Contrast Test (Black with Yellow Border)</h4>
      <button
        ref={containerRef as React.RefObject<HTMLButtonElement>}
        onClick={handleClick}
        disabled={isLoading}
        style={{
          padding: "16px 32px",
          borderRadius: "6px",
          border: "3px solid #333",
          backgroundColor: "#333",
          color: "white",
          cursor: isLoading ? "not-allowed" : "pointer",
          position: "relative",
          minWidth: "180px",
          fontSize: "16px",
          fontWeight: "bold",
        }}
      >
        <span ref={textRef as React.RefObject<HTMLSpanElement>}>High Contrast Test</span>
        <div
          ref={iconRef as React.RefObject<HTMLDivElement>}
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%) scale(0)",
            opacity: 0,
            color: "#ffff00",
            fontSize: "20px",
          }}
        >
          🌟
        </div>
      </button>
    </div>
  );
};
