import React, { useRef, useState } from 'react';
import { useLoadingAnimation } from '../hooks/useLoadingAnimation';

export const BorderTestButton: React.FC = () => {
  const containerRef = useRef<HTMLElement>(null);
  const textRef = useRef<HTMLElement>(null);
  const iconRef = useRef<HTMLElement>(null);
  
  const [isLoading, setIsLoading] = useState(false);
  
  // Test with a very visible border color
  const { startLoading, goBack, cleanup } = useLoadingAnimation({
    shapeDuration: 800,
    backgroundDuration: 600,
    iconDuration: 400,
    spinDuration: 2000, // Slower spin to see the border better
    easing: "easeOutCubic",
    borderColor: "#ff0000", // Bright red border
    circleSize: 60,
  });
  
  React.useEffect(() => {
    return cleanup;
  }, [cleanup]);
  
  const handleClick = () => {
    if (!isLoading) {
      setIsLoading(true);
      startLoading({ containerRef, textRef, iconRef });
      
      // Longer duration to observe the animation
      setTimeout(() => {
        setIsLoading(false);
        goBack({ containerRef, textRef, iconRef });
      }, 8000);
    }
  };
  
  return (
    <div style={{ padding: '20px' }}>
      <h3>Border Test Button</h3>
      <p>This button should show a bright red border during the spin animation.</p>
      
      <button
        ref={containerRef as React.RefObject<HTMLButtonElement>}
        onClick={handleClick}
        disabled={isLoading}
        style={{
          padding: '16px 32px',
          borderRadius: '8px',
          border: '2px solid #007bff',
          backgroundColor: '#007bff',
          color: 'white',
          cursor: isLoading ? 'not-allowed' : 'pointer',
          position: 'relative',
          minWidth: '160px',
          fontSize: '16px',
          fontWeight: 'bold',
        }}
      >
        <span ref={textRef as React.RefObject<HTMLSpanElement>}>
          Test Border Animation
        </span>
        <div
          ref={iconRef as React.RefObject<HTMLDivElement>}
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%) scale(0)',
            opacity: 0,
            fontSize: '24px',
          }}
        >
          🔄
        </div>
      </button>
      
      <div style={{ marginTop: '20px', fontSize: '14px', color: '#666' }}>
        <p><strong>Expected behavior:</strong></p>
        <ul>
          <li>1. Text fades out</li>
          <li>2. Button transforms to circle</li>
          <li>3. Background becomes transparent</li>
          <li>4. <strong>RED BORDER should be visible (3/4 circle)</strong></li>
          <li>5. Icon appears and spins</li>
          <li>6. After 8 seconds, reverses back to original state</li>
        </ul>
      </div>
    </div>
  );
};

// Simple inline test to verify border styles
export const StaticBorderTest: React.FC = () => {
  return (
    <div style={{ padding: '20px' }}>
      <h3>Static Border Test</h3>
      <p>These elements show what the borders should look like:</p>
      
      <div style={{ display: 'flex', gap: '20px', alignItems: 'center' }}>
        {/* Full border circle */}
        <div
          style={{
            width: '60px',
            height: '60px',
            borderRadius: '50%',
            border: '2px solid #ff0000',
            backgroundColor: 'transparent',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          Full
        </div>
        
        {/* 3/4 border circle */}
        <div
          style={{
            width: '60px',
            height: '60px',
            borderRadius: '50%',
            border: '2px solid #ff0000',
            borderBottom: '2px solid transparent',
            backgroundColor: 'transparent',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          3/4
        </div>
        
        {/* Alternative 3/4 border approach */}
        <div
          style={{
            width: '60px',
            height: '60px',
            borderRadius: '50%',
            borderTop: '2px solid #ff0000',
            borderRight: '2px solid #ff0000',
            borderLeft: '2px solid #ff0000',
            borderBottom: 'none',
            backgroundColor: 'transparent',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          Alt
        </div>
      </div>
    </div>
  );
};
